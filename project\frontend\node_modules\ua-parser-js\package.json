{"title": "UAParser.js", "name": "ua-parser-js", "version": "2.0.5", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://faisalman.com)", "description": "Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent & Client Hints data. Supports browser & node.js environment", "keywords": ["user-agent", "client-hints", "parser", "browser", "engine", "os", "device", "cpu", "jquery-plugin", "ecosystem:jquery", "ua-parser-js", "browser-detection", "device-detection", "os-detection", "bot-detection"], "homepage": "https://uaparser.dev", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "Admas <<EMAIL>>", "Aiyush <<EMAIL>>", "algenon <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON>u <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <arun<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "Beat YT <<EMAIL>>", "Bendeguz <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "boneyao <<EMAIL>>", "<PERSON> <carlchrist<PERSON><PERSON><PERSON>@gmail.com>", "CESAR RAMOS <<EMAIL>>", "<PERSON> <<EMAIL>>", "chenhui9279 <<EMAIL>>", "chenyuan-new <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <d.vladim<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "ddivernois <<EMAIL>>", "Deliaz <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "dhoko <<EMAIL>>", "dianhe <<EMAIL>>", "dineshks1 <dineshks1@<EMAIL>>", "<PERSON> <d<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "gulpin <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <74898239+haral<PERSON><PERSON><PERSON><PERSON>-<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "hr6r <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Hyunbin <47051820+hyunbinse<PERSON>@users.noreply.github.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <i.kamal<PERSON>@adguard.com>", "<PERSON><PERSON> <<EMAIL>>", "insanehong <<EMAIL>>", "jackpoll <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "J<PERSON><PERSON><PERSON> <12983479+JB<PERSON><PERSON>@users.noreply.github.com>", "<PERSON> <joey<PERSON><PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <joshua<PERSON><PERSON><PERSON>@outlook.com>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "kNoAPP <<EMAIL>>", "<PERSON> Treveil <<EMAIL>>", "leonardo <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Lithin <<EMAIL>>", "liujunlve <<EMAIL>>", "lj0812 <<EMAIL>>", "ll-syber <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Malash <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <masa<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Max Maurer <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <mike<PERSON><PERSON><PERSON>@me.com>", "MimyyK <<EMAIL>>", "Mok <<EMAIL>>", "nabetama <<EMAIL>>", "naoh <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Nik <PERSON> <<EMAIL>>", "nionata <<EMAIL>>", "niris <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "o.drapeza <<EMAIL>>", "<PERSON> <<EMAIL>>", "otakuSiD <<EMAIL>>", "<PERSON> <<EMAIL>>", "Paris Morgan <<EMAIL>>", "patrick-nurt <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> Hello <<PERSON><PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>", "philippsimon <<EMAIL>>", "<PERSON> <<EMAIL>>", "Piper Chester <<EMAIL>>", "Queen <PERSON><PERSON>ch <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <riley<PERSON><PERSON>@users.noreply.github.com>", "<PERSON> <<EMAIL>>", "roman.savarin <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "ruicong <<EMAIL>>", "Runar Heggset <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> Sonntag <<EMAIL>>", "sgautrea <<EMAIL>>", "shahar<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "St<PERSON>el <<EMAIL>>", "sunny-mwx <<EMAIL>>", "sUP <<EMAIL>>", "Sylva<PERSON> <<EMAIL>>", "szchenghuang <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "XhmikosR <<EMAIL>>", "Y<PERSON>lmaz <<EMAIL>>", "yuanyang <<EMAIL>>", "<PERSON>-jin <<EMAIL>>", "<PERSON> <<EMAIL>>", "Ziding <PERSON> <<EMAIL>>"], "type": "commonjs", "types": "src/main/ua-parser.d.ts", "main": "src/main/ua-parser.js", "module": "src/main/ua-parser.mjs", "browser": "dist/ua-parser.pack.js", "exports": {".": {"require": "./src/main/ua-parser.js", "import": "./src/main/ua-parser.mjs", "types": "./src/main/ua-parser.d.ts"}, "./enums": {"require": "./src/enums/ua-parser-enums.js", "import": "./src/enums/ua-parser-enums.mjs", "types": "./src/enums/ua-parser-enums.d.ts"}, "./extensions": {"require": "./src/extensions/ua-parser-extensions.js", "import": "./src/extensions/ua-parser-extensions.mjs", "types": "./src/extensions/ua-parser-extensions.d.ts"}, "./helpers": {"require": "./src/helpers/ua-parser-helpers.js", "import": "./src/helpers/ua-parser-helpers.mjs", "types": "./src/helpers/ua-parser-helpers.d.ts"}}, "files": ["dist", "src"], "bin": "./script/cli.js", "scripts": {"build": "./script/build-dist.sh && ./script/build-esm.js", "build+test": "npm run build && npm run test", "fuzz": "jazzer ./test/fuzz/redos.js --sync", "test": "./script/test-all.sh", "test:dts-lint": "tsd --typings src/main/ua-parser.d.ts --files test/static/dts-lint.ts", "test:eslint": "eslint src && eslint script", "test:jshint": "jshint src/main", "test:lockfile-lint": "npx lockfile-lint -p package-lock.json", "test:mocha": "mocha test/unit", "test:playwright": "npx playwright install && playwright test test/e2e --browser all"}, "dependencies": {"detect-europe-js": "^0.1.2", "is-standalone-pwa": "^0.1.1", "ua-is-frozen": "^0.1.2", "undici": "^7.12.0"}, "devDependencies": {"@babel/parser": "7.15.8", "@babel/traverse": "7.23.2", "@playwright/test": "^1.49.0", "jshint": "~2.13.6", "mocha": "~8.2.0", "requirejs": "2.3.2", "safe-regex": "^2.1.1", "tsd": "^0.29.0", "uglify-js": "~3.19.3"}, "repository": {"type": "git", "url": "https://github.com/faisalman/ua-parser-js.git"}, "license": "AGPL-3.0-or-later", "engines": {"node": "*"}, "directories": {"dist": "dist", "script": "script", "src": "src", "test": "test"}, "bugs": "https://github.com/faisalman/ua-parser-js/issues", "demo": "https://uaparser.dev", "download": "https://raw.github.com/faisalman/ua-parser-js/master/dist/ua-parser.pack.js", "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}]}