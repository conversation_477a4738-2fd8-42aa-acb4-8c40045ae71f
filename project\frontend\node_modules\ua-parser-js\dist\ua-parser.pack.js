/* UAParser.js v2.0.5
   Copyright © 2012-2025 <PERSON><PERSON><PERSON> <<EMAIL>>
   AGPLv3 License */
((i,c)=>{function V(i){for(var e={},t=0;t<i.length;t++)e[i[t].toUpperCase()]=i[t];return e}var I=500,U="user-agent",d="",B="?",L="function",n="undefined",l="object",R="string",u="browser",h="cpu",p="device",m="engine",f="os",g="result",v="name",k="type",x="vendor",y="version",C="architecture",G="major",S="model",W="console",_="mobile",r="tablet",e="smarttv",t="wearable",N="xr",F="embedded",o="inapp",D="brands",q="formFactors",$="fullVersionList",z="platform",X="platformVersion",Y="bitness",a="sec-ch-ua",Z=a+"-full-version-list",Q=a+"-arch",K=a+"-"+Y,J=a+"-form-factors",ii=a+"-"+_,ei=a+"-"+S,ti=a+"-"+z,oi=ti+"-version",ri=[D,$,_,S,z,X,C,q,Y],ai="Amazon",s="Apple",si="ASUS",ni="BlackBerry",w="Google",wi="Huawei",bi="Lenovo",di="Microsoft",li="Motorola",ci="Nvidia",ui="OnePlus",hi="OPPO",pi="Samsung",mi="Sony",fi="Xiaomi",gi="Zebra",vi="Chromium",b="Chromecast",ki="Edge",xi="Firefox",O="Opera",yi="Facebook",T="Mobile ",Ci=" Browser",Si="Windows",_i=typeof i!==n,A=_i&&i.navigator?i.navigator:c,H=A&&A.userAgentData?A.userAgentData:c,qi=function(i,e){if(typeof i===l&&0<i.length){for(var t in i)if(j(e)==j(i[t]))return!0;return!1}return!!Oi(i)&&j(e)==j(i)},zi=function(i,e){for(var t in i)return/^(browser|cpu|device|engine|os)$/.test(t)||!!e&&zi(i[t])},Oi=function(i){return typeof i===R},Ti=function(i){if(!i)return c;for(var e,t=[],o=Hi(/\\?\"/g,i).split(","),r=0;r<o.length;r++)-1<o[r].indexOf(";")?(e=Mi(o[r]).split(";v="),t[r]={brand:e[0],version:e[1]}):t[r]=Mi(o[r]);return t},j=function(i){return Oi(i)?i.toLowerCase():i},Ai=function(i){return Oi(i)?Hi(/[^\d\.]/g,i).split(".")[0]:c},M=function(i){for(var e in i)i.hasOwnProperty(e)&&(typeof(e=i[e])==l&&2==e.length?this[e[0]]=e[1]:this[e]=c);return this},Hi=function(i,e){return Oi(e)?e.replace(i,d):e},ji=function(i){return Hi(/\\?\"/g,i)},Mi=function(i,e){if(Oi(i))return i=Hi(/^\s\s*/,i),typeof e===n?i:i.substring(0,I)},Ei=function(i,e){if(i&&e)for(var t,o,r,a,s,n=0;n<e.length&&!a;){for(var w=e[n],b=e[n+1],d=t=0;d<w.length&&!a&&w[d];)if(a=w[d++].exec(i))for(o=0;o<b.length;o++)s=a[++t],typeof(r=b[o])===l&&0<r.length?2===r.length?typeof r[1]==L?this[r[0]]=r[1].call(this,s):this[r[0]]=r[1]:3<=r.length&&(typeof r[1]!==L||r[1].exec&&r[1].test?3==r.length?this[r[0]]=s?s.replace(r[1],r[2]):c:4==r.length?this[r[0]]=s?r[3].call(this,s.replace(r[1],r[2])):c:4<r.length&&(this[r[0]]=s?r[3].apply(this,[s.replace(r[1],r[2])].concat(r.slice(4))):c):3<r.length?this[r[0]]=s?r[1].apply(this,r.slice(2)):c:this[r[0]]=s?r[1].call(this,s,r[2]):c):this[r]=s||c;n+=2}},E=function(i,e){for(var t in e)if(typeof e[t]===l&&0<e[t].length){for(var o=0;o<e[t].length;o++)if(qi(e[t][o],i))return t===B?c:t}else if(qi(e[t],i))return t===B?c:t;return e.hasOwnProperty("*")?e["*"]:i},Pi={ME:"4.90","NT 3.51":"3.51","NT 4.0":"4.0",2e3:["5.0","5.01"],XP:["5.1","5.2"],Vista:"6.0",7:"6.1",8:"6.2",8.1:"6.3",10:["6.4","10.0"],NT:""},Vi={embedded:"Automotive",mobile:"Mobile",tablet:["Tablet","EInk"],smarttv:"TV",wearable:"Watch",xr:["VR","XR"],"?":["Desktop","Unknown"],"*":c},Ii={Chrome:"Google Chrome",Edge:"Microsoft Edge","Edge WebView2":"Microsoft Edge WebView2","Chrome WebView":"Android WebView","Chrome Headless":"HeadlessChrome","Huawei Browser":"HuaweiBrowser","MIUI Browser":"Miui Browser","Opera Mobi":"OperaMobile",Yandex:"YaBrowser"},Ui={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[v,T+"Chrome"]],[/webview.+edge\/([\w\.]+)/i],[y,[v,ki+" WebView"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[v,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[v,y],[/opios[\/ ]+([\w\.]+)/i],[y,[v,O+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[y,[v,O+" GX"]],[/\bopr\/([\w\.]+)/i],[y,[v,O]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[y,[v,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[y,[v,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon|otter|dooble|(?:lg |qute)browser)\/([-\w\.]+)/i,/(heytap|ovi|115|surf)browser\/([\d\.]+)/i,/(ecosia|weibo)(?:__| \w+@)([\d\.]+)/i],[v,y],[/quark(?:pc)?\/([-\w\.]+)/i],[y,[v,"Quark"]],[/\bddg\/([\w\.]+)/i],[y,[v,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[v,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[y,[v,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[v,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[v,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[v,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[y,[v,"Smart "+bi+Ci]],[/(avast|avg)\/([\w\.]+)/i],[[v,/(.+)/,"$1 Secure"+Ci],y],[/\bfocus\/([\w\.]+)/i],[y,[v,xi+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[v,O+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[v,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[v,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[v,O+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[v,"MIUI"+Ci]],[/fxios\/([\w\.-]+)/i],[y,[v,T+xi]],[/\bqihoobrowser\/?([\w\.]*)/i],[y,[v,"360"]],[/\b(qq)\/([\w\.]+)/i],[[v,/(.+)/,"$1Browser"],y],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[v,/(.+)/,"$1"+Ci],y],[/samsungbrowser\/([\w\.]+)/i],[y,[v,pi+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[y,[v,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[v,"Sogou Mobile"],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[v,y],[/(lbbrowser|rekonq)/i],[v],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[y,v],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[v,yi],y,[k,o]],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/(daum)apps[\/ ]([\w\.]+)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(instagram|snapchat|klarna)[\/ ]([-\w\.]+)/i],[v,y,[k,o]],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[v,"GSA"],[k,o]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[v,"TikTok"],[k,o]],[/\[(linkedin)app\]/i],[v,[k,o]],[/(zalo(?:app)?)[\/\sa-z]*([\w\.-]+)/i],[[v,/(.+)/,"Zalo"],y,[k,o]],[/(chromium)[\/ ]([-\w\.]+)/i],[v,y],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[v,"Chrome Headless"]],[/wv\).+chrome\/([\w\.]+).+edgw\//i],[y,[v,ki+" WebView2"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[v,"Chrome WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[v,"Android"+Ci]],[/chrome\/([\w\.]+) mobile/i],[y,[v,T+"Chrome"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[v,y],[/version\/([\w\.\,]+) .*mobile(?:\/\w+ | ?)safari/i],[y,[v,T+"Safari"]],[/iphone .*mobile(?:\/\w+ | ?)safari/i],[[v,T+"Safari"]],[/version\/([\w\.\,]+) .*(safari)/i],[y,v],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[v,[y,"1"]],[/(webkit|khtml)\/([\w\.]+)/i],[v,y],[/(?:mobile|tablet);.*(firefox)\/([\w\.-]+)/i],[[v,T+xi],y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[v,"Netscape"],y],[/(wolvic|librewolf)\/([\w\.]+)/i],[v,y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[v,xi+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(amaya|dillo|doris|icab|ladybird|lynx|mosaic|netsurf|obigo|polaris|w3m|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/\b(links) \(([\w\.]+)/i],[v,[y,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[v,[y,/[^\d\.]+./,d]]],cpu:[[/\b((amd|x|x86[-_]?|wow|win)64)\b/i],[[C,"amd64"]],[/(ia32(?=;))/i,/\b((i[346]|x)86)(pc)?\b/i],[[C,"ia32"]],[/\b(aarch64|arm(v?[89]e?l?|_?64))\b/i],[[C,"arm64"]],[/\b(arm(v[67])?ht?n?[fl]p?)\b/i],[[C,"armhf"]],[/( (ce|mobile); ppc;|\/[\w\.]+arm\b)/i],[[C,"arm"]],[/ sun4\w[;\)]/i],[[C,"sparc"]],[/\b(avr32|ia64(?=;)|68k(?=\))|\barm(?=v([1-7]|[5-7]1)l?|;|eabi)|(irix|mips|sparc)(64)?\b|pa-risc)/i,/((ppc|powerpc)(64)?)( mac|;|\))/i,/(?:osf1|[freopnt]{3,4}bsd) (alpha)/i],[[C,/ower/,d,j]],[/winnt.+\[axp/i],[[C,"alpha"]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[S,[x,pi],[k,r]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr]|browser)[-\w]+)/i,/sec-(sgh\w+)/i],[S,[x,pi],[k,_]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[S,[x,s],[k,_]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[S,[x,s],[k,r]],[/(macintosh);/i],[S,[x,s]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[S,[x,"Sharp"],[k,_]],[/\b((?:brt|eln|hey2?|gdi|jdn)-a?[lnw]09|(?:ag[rm]3?|jdn2|kob2)-a?[lw]0[09]hn)(?: bui|\)|;)/i],[S,[x,"Honor"],[k,r]],[/honor([-\w ]+)[;\)]/i],[S,[x,"Honor"],[k,_]],[/\b((?:ag[rs][2356]?k?|bah[234]?|bg[2o]|bt[kv]|cmr|cpn|db[ry]2?|jdn2|got|kob2?k?|mon|pce|scm|sht?|[tw]gr|vrd)-[ad]?[lw][0125][09]b?|605hw|bg2-u03|(?:gem|fdr|m2|ple|t1)-[7a]0[1-4][lu]|t1-a2[13][lw]|mediapad[\w\. ]*(?= bui|\)))\b(?!.+d\/s)/i],[S,[x,wi],[k,r]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[S,[x,wi],[k,_]],[/oid[^\)]+; (2[\dbc]{4}(182|283|rp\w{2})[cgl]|m2105k81a?c)(?: bui|\))/i,/\b((?:red)?mi[-_ ]?pad[\w- ]*)(?: bui|\))/i],[[S,/_/g," "],[x,fi],[k,r]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i,/ ([\w ]+) miui\/v?\d/i],[[S,/_/g," "],[x,fi],[k,_]],[/droid.+; (cph2[3-6]\d[13579]|((gm|hd)19|(ac|be|in|kb)20|(d[en]|eb|le|mt)21|ne22)[0-2]\d|p[g-k]\w[1m]10)\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[S,[x,ui],[k,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[S,[x,hi],[k,_]],[/\b(opd2(\d{3}a?))(?: bui|\))/i],[S,[x,E,{OnePlus:["203","304","403","404","413","415"],"*":hi}],[k,r]],[/(vivo (5r?|6|8l?|go|one|s|x[il]?[2-4]?)[\w\+ ]*)(?: bui|\))/i],[S,[x,"BLU"],[k,_]],[/; vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[S,[x,"Vivo"],[k,_]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[S,[x,"Realme"],[k,_]],[/(ideatab[-\w ]+|602lv|d-42a|a101lv|a2109a|a3500-hv|s[56]000|pb-6505[my]|tb-?x?\d{3,4}(?:f[cu]|xu|[av])|yt\d?-[jx]?\d+[lfmx])( bui|;|\)|\/)/i,/lenovo ?(b[68]0[08]0-?[hf]?|tab(?:[\w- ]+?)|tb[\w-]{6,7})( bui|;|\)|\/)/i],[S,[x,bi],[k,r]],[/lenovo[-_ ]?([-\w ]+?)(?: bui|\)|\/)/i],[S,[x,bi],[k,_]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ]([\w\s]+)(\)| bui)/i,/((?:moto(?! 360)[-\w\(\) ]+|xt\d{3,4}[cgkosw\+]?[-\d]*|nexus 6)(?= bui|\)))/i],[S,[x,li],[k,_]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[S,[x,li],[k,r]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[S,[x,"LG"],[k,r]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+(?!.*(?:browser|netcast|android tv|watch|webos))(\w+)/i,/\blg-?([\d\w]+) bui/i],[S,[x,"LG"],[k,_]],[/(nokia) (t[12][01])/i],[x,S,[k,r]],[/(?:maemo|nokia).*(n900|lumia \d+|rm-\d+)/i,/nokia[-_ ]?(([-\w\. ]*))/i],[[S,/_/g," "],[k,_],[x,"Nokia"]],[/(pixel (c|tablet))\b/i],[S,[x,w],[k,r]],[/droid.+;(?: google)? (g(01[13]a|020[aem]|025[jn]|1b60|1f8f|2ybb|4s1m|576d|5nz6|8hhn|8vou|a02099|c15s|d1yq|e2ae|ec77|gh2x|kv4x|p4bc|pj41|r83y|tt9q|ur25|wvk6)|pixel[\d ]*a?( pro)?( xl)?( fold)?( \(5g\))?)( bui|\))/i],[S,[x,w],[k,_]],[/(google) (pixelbook( go)?)/i],[x,S],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-\w\w\d\d)(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[S,[x,mi],[k,_]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[S,"Xperia Tablet"],[x,mi],[k,r]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[S,[x,ai],[k,r]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[S,/(.+)/g,"Fire Phone $1"],[x,ai],[k,_]],[/(playbook);[-\w\),; ]+(rim)/i],[S,x,[k,r]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[S,[x,ni],[k,_]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[S,[x,si],[k,r]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[S,[x,si],[k,_]],[/(nexus 9)/i],[S,[x,"HTC"],[k,r]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[x,[S,/_/g," "],[k,_]],[/tcl (xess p17aa)/i,/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\w(\w|\w\w))?(\)| bui)/i],[S,[x,"TCL"],[k,r]],[/droid [\w\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\w(\w|\w\w))?(\)| bui)/i],[S,[x,"TCL"],[k,_]],[/(itel) ((\w+))/i],[[x,j],S,[k,E,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[S,[x,"Acer"],[k,r]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[S,[x,"Meizu"],[k,_]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[S,[x,"Ulefone"],[k,_]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[S,[x,"Energizer"],[k,_]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[S,[x,"Cat"],[k,_]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[S,[x,"Smartfren"],[k,_]],[/droid.+; (a(in)?(0(15|59|6[35])|142)p?)/i],[S,[x,"Nothing"],[k,_]],[/; (x67 5g|tikeasy \w+|ac[1789]\d\w+)( b|\))/i,/archos ?(5|gamepad2?|([\w ]*[t1789]|hello) ?\d+[\w ]*)( b|\))/i],[S,[x,"Archos"],[k,r]],[/archos ([\w ]+)( b|\))/i,/; (ac[3-6]\d\w{2,8})( b|\))/i],[S,[x,"Archos"],[k,_]],[/; (n159v)/i],[S,[x,"HMD"],[k,_]],[/(imo) (tab \w+)/i,/(infinix|tecno) (x1101b?|p904|dp(7c|8d|10a)( pro)?|p70[1-3]a?|p904|t1101)/i],[x,S,[k,r]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus(?! zenw)|dell|jolla|meizu|motorola|polytron|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (blu|hmd|imo|infinix|lava|oneplus|tcl)[_ ]([\w\+ ]+?)(?: bui|\)|; r)/i,/(hp) ([\w ]+\w)/i,/(microsoft); (lumia[\w ]+)/i,/(oppo) ?([\w ]+) bui/i,/droid[^;]+; (philips)[_ ]([sv-x][\d]{3,4}[xz]?)/i],[x,S,[k,_]],[/(kobo)\s(ereader|touch)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i],[x,S,[k,r]],[/(surface duo)/i],[S,[x,di],[k,r]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[S,[x,"Fairphone"],[k,_]],[/((?:tegranote|shield t(?!.+d tv))[\w- ]*?)(?: b|\))/i],[S,[x,ci],[k,r]],[/(sprint) (\w+)/i],[x,S,[k,_]],[/(kin\.[onetw]{3})/i],[[S,/\./g," "],[x,di],[k,_]],[/droid.+; ([c6]+|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[S,[x,gi],[k,r]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[S,[x,gi],[k,_]],[/(philips)[\w ]+tv/i,/smart-tv.+(samsung)/i],[x,[k,e]],[/hbbtv.+maple;(\d+)/i],[[S,/^/,"SmartTV"],[x,pi],[k,e]],[/(vizio)(?: |.+model\/)(\w+-\w+)/i,/tcast.+(lg)e?. ([-\w]+)/i],[x,S,[k,e]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[x,"LG"],[k,e]],[/(apple) ?tv/i],[x,[S,s+" TV"],[k,e]],[/crkey.*devicetype\/chromecast/i],[[S,b+" Third Generation"],[x,w],[k,e]],[/crkey.*devicetype\/([^/]*)/i],[[S,/^/,"Chromecast "],[x,w],[k,e]],[/fuchsia.*crkey/i],[[S,b+" Nest Hub"],[x,w],[k,e]],[/crkey/i],[[S,b],[x,w],[k,e]],[/(portaltv)/i],[S,[x,yi],[k,e]],[/droid.+aft(\w+)( bui|\))/i],[S,[x,ai],[k,e]],[/(shield \w+ tv)/i],[S,[x,ci],[k,e]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[S,[x,"Sharp"],[k,e]],[/(bravia[\w ]+)( bui|\))/i],[S,[x,mi],[k,e]],[/(mi(tv|box)-?\w+) bui/i],[S,[x,fi],[k,e]],[/Hbbtv.*(technisat) (.*);/i],[x,S,[k,e]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[x,/.+\/(\w+)/,"$1",E,{LG:"lge"}],[S,Mi],[k,e]],[/(playstation \w+)/i],[S,[x,mi],[k,W]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[S,[x,di],[k,W]],[/(ouya)/i,/(nintendo) (\w+)/i,/(retroid) (pocket ([^\)]+))/i],[x,S,[k,W]],[/droid.+; (shield)( bui|\))/i],[S,[x,ci],[k,W]],[/\b(sm-[lr]\d\d[0156][fnuw]?s?|gear live)\b/i],[S,[x,pi],[k,t]],[/((pebble))app/i,/(asus|google|lg|oppo) ((pixel |zen)?watch[\w ]*)( bui|\))/i],[x,S,[k,t]],[/(ow(?:19|20)?we?[1-3]{1,3})/i],[S,[x,hi],[k,t]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[S,[x,s],[k,t]],[/(opwwe\d{3})/i],[S,[x,ui],[k,t]],[/(moto 360)/i],[S,[x,li],[k,t]],[/(smartwatch 3)/i],[S,[x,mi],[k,t]],[/(g watch r)/i],[S,[x,"LG"],[k,t]],[/droid.+; (wt63?0{2,3})\)/i],[S,[x,gi],[k,t]],[/droid.+; (glass) \d/i],[S,[x,w],[k,N]],[/(pico) ([\w ]+) os\d/i],[x,S,[k,N]],[/(quest( \d| pro)?s?).+vr/i],[S,[x,yi],[k,N]],[/mobile vr; rv.+firefox/i],[[k,N]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[x,[k,F]],[/(aeobc)\b/i],[S,[x,ai],[k,F]],[/(homepod).+mac os/i],[S,[x,s],[k,F]],[/windows iot/i],[[k,F]],[/droid.+; ([\w- ]+) (4k|android|smart|google)[- ]?tv/i],[S,[k,e]],[/\b((4k|android|smart|opera)[- ]?tv|tv; rv:|large screen[\w ]+safari)\b/i],[[k,e]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+?(mobile|vr|\d) safari/i],[S,[k,E,{mobile:"Mobile",xr:"VR","*":r}]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[k,r]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[k,_]],[/droid .+?; ([\w\. -]+)( bui|\))/i],[S,[x,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[v,ki+"HTML"]],[/(arkweb)\/([\w\.]+)/i],[v,y],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[v,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[v,y],[/ladybird\//i],[[v,"LibWeb"]],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,v]],os:[[/(windows nt) (6\.[23]); arm/i],[[v,/N/,"R"],[y,E,Pi]],[/(windows (?:phone|mobile|iot))(?: os)?[\/ ]?([\d\.]*( se)?)/i,/(windows)[\/ ](1[01]|2000|3\.1|7|8(\.1)?|9[58]|me|server 20\d\d( r2)?|vista|xp)/i],[v,y],[/windows nt ?([\d\.\)]*)(?!.+xbox)/i,/\bwin(?=3| ?9|n)(?:nt| 9x )?([\d\.;]*)/i],[[y,/(;|\))/g,"",E,Pi],[v,Si]],[/(windows ce)\/?([\d\.]*)/i],[v,y],[/[adehimnop]{4,7}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[v,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+(haiku|morphos))/i],[[v,"macOS"],[y,/_/g,"."]],[/android ([\d\.]+).*crkey/i],[y,[v,b+" Android"]],[/fuchsia.*crkey\/([\d\.]+)/i],[y,[v,b+" Fuchsia"]],[/crkey\/([\d\.]+).*devicetype\/smartspeaker/i],[y,[v,b+" SmartSpeaker"]],[/linux.*crkey\/([\d\.]+)/i],[y,[v,b+" Linux"]],[/crkey\/([\d\.]+)/i],[y,[v,b]],[/droid ([\w\.]+)\b.+(android[- ]x86)/i],[y,v],[/(ubuntu) ([\w\.]+) like android/i],[[v,/(.+)/,"$1 Touch"],y],[/(harmonyos)[\/ ]?([\d\.]*)/i,/(android|bada|blackberry|kaios|maemo|meego|openharmony|qnx|rim tablet os|sailfish|series40|symbian|tizen)\w*[-\/\.; ]?([\d\.]*)/i],[v,y],[/\(bb(10);/i],[y,[v,ni]],[/(?:symbian ?os|symbos|s60(?=;)|series ?60)[-\/ ]?([\w\.]*)/i],[y,[v,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[v,xi+" OS"]],[/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i,/webos(?:[ \/]?|\.tv-20(?=2[2-9]))(\d[\d\.]*)/i],[y,[v,"webOS"]],[/web0s;.+?(?:chr[o0]me|safari)\/(\d+)/i],[[y,E,{25:"120",24:"108",23:"94",22:"87",6:"79",5:"68",4:"53",3:"38",2:"538",1:"537","*":"TV"}],[v,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[v,"watchOS"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[v,"Chrome OS"],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) (\w+)/i,/(xbox); +xbox ([^\);]+)/i,/(pico) .+os([\w\.]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/linux.+(mint)[\/\(\) ]?([\w\.]*)/i,/(mageia|vectorlinux|fuchsia|arcaos|arch(?= ?linux))[;l ]([\d\.]*)/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire|knoppix)(?: gnu[\/ ]linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/\b(aix)[; ]([1-9\.]{0,4})/i,/(hurd|linux|morphos)(?: (?:arm|x86|ppc)\w*| ?)([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) ?(r\d)?/i],[v,y],[/(sunos) ?([\d\.]*)/i],[[v,"Solaris"],y],[/\b(beos|os\/2|amigaos|openvms|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[v,y]]},Bi=(O={init:{},isIgnore:{},isIgnoreRgx:{},toString:{}},M.call(O.init,[[u,[v,y,G,k]],[h,[C]],[p,[k,S,x]],[m,[v,y]],[f,[v,y]]]),M.call(O.isIgnore,[[u,[y,G]],[m,[y]],[f,[y]]]),M.call(O.isIgnoreRgx,[[u,/ ?browser$/i],[f,/ ?os$/i]]),M.call(O.toString,[[u,[v,y]],[h,[C]],[p,[x,S]],[m,[v,y]],[f,[v,y]]]),O),Li=function(e,i){var t=Bi.init[i],o=Bi.isIgnore[i]||0,r=Bi.isIgnoreRgx[i]||0,a=Bi.toString[i]||0;function s(){M.call(this,t)}return s.prototype.getItem=function(){return e},s.prototype.withClientHints=function(){return H?H.getHighEntropyValues(ri).then(function(i){return e.setCH(new Ri(i,!1)).parseCH().get()}):e.parseCH().get()},s.prototype.withFeatureCheck=function(){return e.detectFeature().get()},i!=g&&(s.prototype.is=function(i){var e,t=!1;for(e in this)if(this.hasOwnProperty(e)&&!qi(o,e)&&j(r?Hi(r,this[e]):this[e])==j(r?Hi(r,i):i)){if(t=!0,i!=n)break}else if(i==n&&t){t=!t;break}return t},s.prototype.toString=function(){var i,e=d;for(i in a)typeof this[a[i]]!==n&&(e+=(e?" ":d)+this[a[i]]);return e||n}),H||(s.prototype.then=function(i){function e(){for(var i in t)t.hasOwnProperty(i)&&(this[i]=t[i])}var t=this,o=(e.prototype={is:s.prototype.is,toString:s.prototype.toString},new e);return i(o),o}),new s};function Ri(i,e){if(i=i||{},M.call(this,ri),e)M.call(this,[[D,Ti(i[a])],[$,Ti(i[Z])],[_,/\?1/.test(i[ii])],[S,ji(i[ei])],[z,ji(i[ti])],[X,ji(i[oi])],[C,ji(i[Q])],[q,Ti(i[J])],[Y,ji(i[K])]]);else for(var t in i)this.hasOwnProperty(t)&&typeof i[t]!==n&&(this[t]=i[t])}function Gi(i,e,t,o){return this.get=function(i){return i?this.data.hasOwnProperty(i)?this.data[i]:c:this.data},this.set=function(i,e){return this.data[i]=e,this},this.setCH=function(i){return this.uaCH=i,this},this.detectFeature=function(){if(A&&A.userAgent==this.ua)switch(this.itemType){case u:A.brave&&typeof A.brave.isBrave==L&&this.set(v,"Brave");break;case p:!this.get(k)&&H&&H[_]&&this.set(k,_),"Macintosh"==this.get(S)&&A&&typeof A.standalone!==n&&A.maxTouchPoints&&2<A.maxTouchPoints&&this.set(S,"iPad").set(k,r);break;case f:!this.get(v)&&H&&H[z]&&this.set(v,H[z]);break;case g:var e=this.data,i=function(i){return e[i].getItem().detectFeature().get()};this.set(u,i(u)).set(h,i(h)).set(p,i(p)).set(m,i(m)).set(f,i(f))}return this},this.parseUA=function(){return this.itemType!=g&&Ei.call(this.data,this.ua,this.rgxMap),this.itemType==u&&this.set(G,Ai(this.get(y))),this},this.parseCH=function(){var i,e=this.uaCH,t=this.rgxMap;switch(this.itemType){case u:case m:var o,r=e[$]||e[D];if(r)for(var a=0;a<r.length;a++){var s=r[a].brand||r[a],n=r[a].version;this.itemType==u&&!/not.a.brand/i.test(s)&&(!o||/Chrom/.test(o)&&s!=vi||o==ki&&/WebView2/.test(s))&&(s=E(s,Ii),(o=this.get(v))&&!/Chrom/.test(o)&&/Chrom/.test(s)||this.set(v,s).set(y,n).set(G,Ai(n)),o=s),this.itemType==m&&s==vi&&this.set(y,n)}break;case h:var w=e[C];w&&("64"==e[Y]&&(w+="64"),Ei.call(this.data,w+";",t));break;case p:if(e[_]&&this.set(k,_),e[S]&&(this.set(S,e[S]),this.get(k)&&this.get(x)||(Ei.call(w={},"droid 9; "+e[S]+")",t),!this.get(k)&&w.type&&this.set(k,w.type),!this.get(x)&&w.vendor&&this.set(x,w.vendor))),e[q]){if("string"!=typeof e[q])for(var b=0;!i&&b<e[q].length;)i=E(e[q][b++],Vi);else i=E(e[q],Vi);this.set(k,i)}break;case f:var d,w=e[z];w&&(d=e[X],w==Si&&(d=13<=parseInt(Ai(d),10)?"11":"10"),this.set(v,w).set(y,d)),this.get(v)==Si&&"Xbox"==e[S]&&this.set(v,"Xbox").set(y,c);break;case g:var l=this.data,w=function(i){return l[i].getItem().setCH(e).parseCH().get()};this.set(u,w(u)).set(h,w(h)).set(p,w(p)).set(m,w(m)).set(f,w(f))}return this},M.call(this,[["itemType",i],["ua",e],["uaCH",o],["rgxMap",t],["data",Li(this,i)]]),this}function P(i,e,t){if(typeof i===l?(e=zi(i,!0)?(typeof e===l&&(t=e),i):(t=i,c),i=c):typeof i!==R||zi(e,!0)||(t=e,e=c),t)if(typeof t.append===L){var o={};t.forEach(function(i,e){o[String(e).toLowerCase()]=i}),t=o}else{var r,a={};for(r in t)t.hasOwnProperty(r)&&(a[String(r).toLowerCase()]=t[r]);t=a}var s,n,w,b;return this instanceof P?(s=typeof i===R?i:t&&t[U]?t[U]:A&&A.userAgent?A.userAgent:d,n=new Ri(t,!0),w=e?((i,e)=>{var t,o={},r=e;if(!zi(e))for(var a in r={},e)for(var s in e[a])r[s]=e[a][s].concat(r[s]||[]);for(t in i)o[t]=r[t]&&r[t].length%2==0?r[t].concat(i[t]):i[t];return o})(Ui,e):Ui,M.call(this,[["getBrowser",(b=function(i){return i==g?function(){return new Gi(i,s,w,n).set("ua",s).set(u,this.getBrowser()).set(h,this.getCPU()).set(p,this.getDevice()).set(m,this.getEngine()).set(f,this.getOS()).get()}:function(){return new Gi(i,s,w[i],n).parseUA().get()}})(u)],["getCPU",b(h)],["getDevice",b(p)],["getEngine",b(m)],["getOS",b(f)],["getResult",b(g)],["getUA",function(){return s}],["setUA",function(i){return Oi(i)&&(s=i.length>I?Mi(i,I):i),this}]]).setUA(s),this):new P(i,e,t).getResult()}P.VERSION="2.0.5",P.BROWSER=V([v,y,G,k]),P.CPU=V([C]),P.DEVICE=V([S,x,k,W,_,e,r,t,F]),P.ENGINE=P.OS=V([v,y]),typeof exports!==n?(exports=typeof module!==n&&module.exports?module.exports=P:exports).UAParser=P:typeof define===L&&define.amd?define(function(){return P}):_i&&(i.UAParser=P);var Wi,Ni=_i&&(i.jQuery||i.Zepto);Ni&&!Ni.ua&&(Wi=new P,Ni.ua=Wi.getResult(),Ni.ua.get=function(){return Wi.getUA()},Ni.ua.set=function(i){Wi.setUA(i);var e,t=Wi.getResult();for(e in t)Ni.ua[e]=t[e]})})("object"==typeof window?window:this);