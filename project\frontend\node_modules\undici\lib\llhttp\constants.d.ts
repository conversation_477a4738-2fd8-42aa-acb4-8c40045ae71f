export type IntDict = Record<string, number>;
export declare const ERROR: IntDict;
export declare const TYPE: IntDict;
export declare const FLAGS: IntDict;
export declare const LENIENT_FLAGS: IntDict;
export declare const METHODS: IntDict;
export declare const STATUSES: IntDict;
export declare const FINISH: IntDict;
export declare const HEADER_STATE: IntDict;
export declare const METHODS_HTTP: number[];
export declare const METHODS_ICE: number[];
export declare const METHODS_RTSP: number[];
export declare const METHOD_MAP: IntDict;
export declare const H_METHOD_MAP: {
    [k: string]: number;
};
export declare const STATUSES_HTTP: number[];
export type CharList = (string | number)[];
export declare const ALPHA: CharList;
export declare const NUM_MAP: {
    0: number;
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
    6: number;
    7: number;
    8: number;
    9: number;
};
export declare const HEX_MAP: {
    0: number;
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
    6: number;
    7: number;
    8: number;
    9: number;
    A: number;
    B: number;
    C: number;
    D: number;
    E: number;
    F: number;
    a: number;
    b: number;
    c: number;
    d: number;
    e: number;
    f: number;
};
export declare const NUM: CharList;
export declare const ALPHANUM: CharList;
export declare const MARK: CharList;
export declare const USERINFO_CHARS: CharList;
export declare const URL_CHAR: CharList;
export declare const HEX: CharList;
export declare const TOKEN: CharList;
export declare const HEADER_CHARS: CharList;
export declare const CONNECTION_TOKEN_CHARS: CharList;
export declare const QUOTED_STRING: CharList;
export declare const HTAB_SP_VCHAR_OBS_TEXT: CharList;
export declare const MAJOR: {
    0: number;
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
    6: number;
    7: number;
    8: number;
    9: number;
};
export declare const MINOR: {
    0: number;
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
    6: number;
    7: number;
    8: number;
    9: number;
};
export declare const SPECIAL_HEADERS: {
    connection: number;
    'content-length': number;
    'proxy-connection': number;
    'transfer-encoding': number;
    upgrade: number;
};
declare const _default: {
    ERROR: IntDict;
    TYPE: IntDict;
    FLAGS: IntDict;
    LENIENT_FLAGS: IntDict;
    METHODS: IntDict;
    STATUSES: IntDict;
    FINISH: IntDict;
    HEADER_STATE: IntDict;
    ALPHA: CharList;
    NUM_MAP: {
        0: number;
        1: number;
        2: number;
        3: number;
        4: number;
        5: number;
        6: number;
        7: number;
        8: number;
        9: number;
    };
    HEX_MAP: {
        0: number;
        1: number;
        2: number;
        3: number;
        4: number;
        5: number;
        6: number;
        7: number;
        8: number;
        9: number;
        A: number;
        B: number;
        C: number;
        D: number;
        E: number;
        F: number;
        a: number;
        b: number;
        c: number;
        d: number;
        e: number;
        f: number;
    };
    NUM: CharList;
    ALPHANUM: CharList;
    MARK: CharList;
    USERINFO_CHARS: CharList;
    URL_CHAR: CharList;
    HEX: CharList;
    TOKEN: CharList;
    HEADER_CHARS: CharList;
    CONNECTION_TOKEN_CHARS: CharList;
    QUOTED_STRING: CharList;
    HTAB_SP_VCHAR_OBS_TEXT: CharList;
    MAJOR: {
        0: number;
        1: number;
        2: number;
        3: number;
        4: number;
        5: number;
        6: number;
        7: number;
        8: number;
        9: number;
    };
    MINOR: {
        0: number;
        1: number;
        2: number;
        3: number;
        4: number;
        5: number;
        6: number;
        7: number;
        8: number;
        9: number;
    };
    SPECIAL_HEADERS: {
        connection: number;
        'content-length': number;
        'proxy-connection': number;
        'transfer-encoding': number;
        upgrade: number;
    };
    METHODS_HTTP: number[];
    METHODS_ICE: number[];
    METHODS_RTSP: number[];
    METHOD_MAP: IntDict;
    H_METHOD_MAP: {
        [k: string]: number;
    };
    STATUSES_HTTP: number[];
};
export default _default;
